"""
Загрузчик конфигурации режимов суммаризации из YAML файла.
Заменяет статическую конфигурацию из models_config.py на динамическую загрузку из YAML.
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from loguru import logger
from google.genai import types


class SummarizeModeConfig:
    """Класс для загрузки и управления конфигурацией режимов суммаризации."""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Инициализация загрузчика конфигурации.
        
        Args:
            config_path: Путь к YAML файлу с конфигурацией. 
                        Если не указан, используется config/summarize_modes.yaml
        """
        if config_path is None:
            # Определяем путь к конфигурации относительно корня проекта
            project_root = Path(__file__).parent.parent.parent
            config_path = project_root / "config" / "summarize_modes.yaml"
        
        self.config_path = Path(config_path)
        self._config_data: Optional[Dict[str, Any]] = None
        self._base_config: Optional[types.GenerateContentConfig] = None
        self._modes: Optional[Dict[str, Dict[str, Any]]] = None
        
        # Загружаем конфигурацию при инициализации
        self.reload_config()
    
    def reload_config(self) -> None:
        """Перезагружает конфигурацию из YAML файла."""
        try:
            if not self.config_path.exists():
                raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
            
            with open(self.config_path, 'r', encoding='utf-8') as file:
                self._config_data = yaml.safe_load(file)
            
            # Валидируем структуру конфигурации
            self._validate_config()
            
            # Создаем базовую конфигурацию
            base_config_data = self._config_data.get('base_config', {})
            self._base_config = types.GenerateContentConfig(
                top_p=base_config_data.get('top_p', 0.95),
                top_k=base_config_data.get('top_k', 64),
                max_output_tokens=base_config_data.get('max_output_tokens', 65536),
                candidate_count=base_config_data.get('candidate_count', 1)
            )
            
            # Сохраняем режимы
            self._modes = self._config_data.get('modes', {})
            
            logger.info(f"Successfully loaded {len(self._modes)} summarization modes from {self.config_path}")
            
        except Exception as e:
            logger.error(f"Failed to load configuration from {self.config_path}: {e}")
            raise
    
    def _validate_config(self) -> None:
        """Валидирует структуру загруженной конфигурации."""
        if not isinstance(self._config_data, dict):
            raise ValueError("Configuration must be a dictionary")
        
        if 'modes' not in self._config_data:
            raise ValueError("Configuration must contain 'modes' section")
        
        modes = self._config_data['modes']
        if not isinstance(modes, dict):
            raise ValueError("'modes' section must be a dictionary")
        
        # Валидируем каждый режим
        for mode_name, mode_config in modes.items():
            if not isinstance(mode_config, dict):
                raise ValueError(f"Mode '{mode_name}' configuration must be a dictionary")
            
            required_fields = ['model_name', 'include_thoughts', 'temperature', 'prompt']
            for field in required_fields:
                if field not in mode_config:
                    raise ValueError(f"Mode '{mode_name}' missing required field: {field}")
            
            # Валидируем типы данных
            if not isinstance(mode_config['model_name'], str):
                raise ValueError(f"Mode '{mode_name}': model_name must be a string")
            
            if not isinstance(mode_config['include_thoughts'], bool):
                raise ValueError(f"Mode '{mode_name}': include_thoughts must be a boolean")
            
            if not isinstance(mode_config['temperature'], (int, float)):
                raise ValueError(f"Mode '{mode_name}': temperature must be a number")
            
            if not isinstance(mode_config['prompt'], str):
                raise ValueError(f"Mode '{mode_name}': prompt must be a string")
            
            # Валидируем диапазоны значений
            if not (0.0 <= mode_config['temperature'] <= 2.0):
                raise ValueError(f"Mode '{mode_name}': temperature must be between 0.0 and 2.0")
    
    @property
    def base_config(self) -> types.GenerateContentConfig:
        """Возвращает базовую конфигурацию для всех моделей."""
        if self._base_config is None:
            raise RuntimeError("Configuration not loaded")
        return self._base_config
    
    @property
    def available_modes(self) -> list[str]:
        """Возвращает список доступных режимов суммаризации."""
        if self._modes is None:
            raise RuntimeError("Configuration not loaded")
        return list(self._modes.keys())
    
    def get_mode_config(self, mode: str) -> Dict[str, Any]:
        """
        Получить полную конфигурацию для указанного режима.
        
        Args:
            mode: Название режима суммаризации
            
        Returns:
            Словарь с конфигурацией режима, включая:
            - model_name: название модели
            - config: объект GenerateContentConfig
            - prompt: системный промпт
            
        Raises:
            ValueError: Если режим не найден
        """
        if self._modes is None:
            raise RuntimeError("Configuration not loaded")
        
        if mode not in self._modes:
            available_modes = ", ".join(self.available_modes)
            raise ValueError(f"Unknown summarization mode: {mode}. Available modes: {available_modes}")
        
        mode_data = self._modes[mode]
        
        # Создаем конфигурацию на основе базовой с обновленными параметрами
        config = types.GenerateContentConfig(
            temperature=mode_data["temperature"],
            top_p=self._base_config.top_p,
            top_k=self._base_config.top_k,
            max_output_tokens=self._base_config.max_output_tokens,
            candidate_count=self._base_config.candidate_count,
            thinking_config=types.ThinkingConfig(
                include_thoughts=mode_data["include_thoughts"]
            ),
        )
        
        return {
            "model_name": mode_data["model_name"],
            "config": config,
            "prompt": mode_data["prompt"],
        }
    
    def get_modes_dict(self) -> Dict[str, Dict[str, Any]]:
        """
        Возвращает словарь всех режимов в формате, совместимом со старым MODEL_CONFIGS.
        Используется для обратной совместимости.
        """
        if self._modes is None:
            raise RuntimeError("Configuration not loaded")
        
        return self._modes.copy()


# Глобальный экземпляр загрузчика конфигурации
_config_loader: Optional[SummarizeModeConfig] = None


def get_config_loader() -> SummarizeModeConfig:
    """Получить глобальный экземпляр загрузчика конфигурации."""
    global _config_loader
    if _config_loader is None:
        _config_loader = SummarizeModeConfig()
    return _config_loader


def reload_config() -> None:
    """Перезагрузить конфигурацию из файла."""
    global _config_loader
    if _config_loader is not None:
        _config_loader.reload_config()


# Функции для обратной совместимости с существующим кодом
def get_model_config(mode: str) -> Dict[str, Any]:
    """
    Получить полную конфигурацию для указанного режима.
    Функция для обратной совместимости с существующим кодом.
    """
    return get_config_loader().get_mode_config(mode)


# Свойство для обратной совместимости
@property
def MODEL_CONFIGS() -> Dict[str, Dict[str, Any]]:
    """Свойство для обратной совместимости со старым MODEL_CONFIGS."""
    return get_config_loader().get_modes_dict()


# Базовая конфигурация для обратной совместимости
@property  
def BASE_CONFIG() -> types.GenerateContentConfig:
    """Базовая конфигурация для обратной совместимости."""
    return get_config_loader().base_config
